import fs from "fs";
import path from "path";

/**
 * Replace all occurrences of "$root.codec.in." by "$root.codec.in_." in the given file
 * @param filePath
 */

function fixPkg(filePath: string) {
  const fullPath = path.resolve(__dirname, filePath);
  let fileContent = fs.readFileSync(fullPath, "utf-8");

  fileContent = fileContent.replace(/\$root\.codec\.in\./g, "$root.codec.in_.");

  fs.writeFileSync(fullPath, fileContent);
}

if (import.meta.main) {
  fixPkg(process.argv[2] ?? "dist/bundle.js");
}
