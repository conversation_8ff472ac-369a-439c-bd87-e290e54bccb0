{"name": "proto", "version": "0.0.1", "description": "Protobuf definitions for Irriga Mais", "private": true, "type": "module", "module": "./dist/bundle.js", "main": "./dist/bundle.js", "types": "./dist/bundle.d.ts", "exports": {".": {"types": "./dist/bundle.d.ts", "bun": "./dist/bundle.js", "node": "./dist/bundle.js", "import": "./dist/bundle.js", "default": "./dist/bundle.js"}}, "files": ["dist"], "scripts": {"protobuf:generate": "pbjs --es6 -t static-module -w es6 -o ./dist/bundle.js ./proto/*.proto && bun run fix-pkg.ts && pbts -o ./dist/bundle.d.ts ./dist/bundle.js", "clean": "[ -d dist ] && rm -vr dist; exit 0", "build": "bun run protobuf:generate", "prebuild": "bun run clean && mkdir -p dist"}, "devDependencies": {"@types/bun": "latest", "protobufjs-cli": "^1.1.3"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"protobufjs": "^7.5.3"}}