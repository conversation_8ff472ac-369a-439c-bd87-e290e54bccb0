import type { SQL } from "bun";
import { getCurrentPropertyDeviceForDevice } from "./property-device-queries";
import { listProjectsWithSchedulingByLICIdentifier } from "./scheduling-queries";
import type { Device } from "./types";
import DB from "../connection";
import { listServiceWaterPumpsByLICIdentifier } from "./water-pump";
import { listReservoirsWithMonitorAndWaterPumpByLICIdentifier } from "./reservoir";

export async function getLICTree(
  db: SQL,
  licIdentifier: string,
  referenceDate: Date
) {
  const lic = await getLIC(db, licIdentifier);
  if (!lic) {
    return null;
  }
  const propertyDevice = await getCurrentPropertyDeviceForDevice(
    db,
    licIdentifier,
    "LIC",
    referenceDate
  );
  const projects = await listProjectsWithSchedulingByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );

  const servicePumps = await listServiceWaterPumpsByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );

  const reservoirs = await listReservoirsWithMonitorAndWaterPumpByLICIdentifier(
    db,
    licIdentifier,
    referenceDate
  );

  return {
    ...lic,
    propertyDevice,
    projects,
    servicePumps,
    reservoirs,
  };
}

export async function getLIC(db: SQL, licIdentifier: string) {
  const [lic] = await db<Device[]>`
        SELECT * FROM device
        WHERE identifier = ${licIdentifier}
        AND model = 'LIC'
        LIMIT 1
    `;
  return lic;
}

export async function listLICS(db: SQL) {
  const lics = await db<Device[]>`
        SELECT * FROM device
        WHERE model = 'LIC'
    `;
  return lics;
}

if (import.meta.main) {
  getLICTree(DB, "9C821FA2B9E8", new Date()).then((tree) => {
    console.log(tree?.date_created);
  });
}
