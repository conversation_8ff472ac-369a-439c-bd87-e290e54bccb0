import type { MqttClient } from "mqtt";
import type {
  CodecTransportMessageCallback,
  CodecTransportUnhandledMessageCallback,
  ICodecTransport,
  ICodecTransportFactory,
} from "../types";
import { handleMessage } from "./handle-message";

export class MQTTTransportFactory implements ICodecTransportFactory {
  private readonly listeners = new Map<
    string,
    CodecTransportMessageCallback[]
  >();
  constructor(
    private client: MqttClient,
    public readonly onUnhandledMessage: CodecTransportUnhandledMessageCallback
  ) {
    this.client.on("message", (topic, payload, packet) => {
      const message = handleMessage(topic, payload, packet);
      const referenceDate = new Date();
      if (message) {
        const callbacks = this.listeners.get(message.deviceId);
        if (callbacks && callbacks.length > 0) {
          callbacks.forEach((callback) =>
            callback(message.topicType, message.payload, referenceDate)
          );
        } else {
          this.onUnhandledMessage(
            message.deviceId,
            message.topicType,
            message.payload,
            referenceDate
          );
        }
      } else {
        //TODO: Handle unknown message
      }
    });
    this.client.subscribe("/codec/+/report");
  }

  //   subscribeReport(licIdentifier: string) {
  //     this.client.subscribe(`/codec/${licIdentifier}/report`);
  //   }

  //   unsubscribeReport(licIdentifier: string) {
  //     this.client.unsubscribe(`/codec/${licIdentifier}/report`);
  //   }

  createTransport(licIdentifier: string): ICodecTransport {
    return {
      sendMessage: async (message) => {
        this.client.publish(`/codec/${licIdentifier}/downlink`, message);
      },
      onMessage: (callback) => {
        if (!this.listeners.has(licIdentifier)) {
          this.listeners.set(licIdentifier, []);
          //   this.subscribeReport(licIdentifier);
        }
        this.listeners.get(licIdentifier)?.push(callback);
        return () => {
          const currentListeners = this.listeners.get(licIdentifier);
          if (currentListeners) {
            currentListeners.splice(currentListeners.indexOf(callback), 1);
            if (currentListeners.length === 0) {
              this.listeners.delete(licIdentifier);
              //   this.unsubscribeReport(licIdentifier);
            }
          }
        };
      },
    };
  }
}
