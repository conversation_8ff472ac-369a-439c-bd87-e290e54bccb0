export type CodecTransportStopCallback = () => void;
export type CodecTransportMessageCallback = (
  topic: string,
  message: Buffer,
  referenceDate: Date
) => void;
export type CodecTransportUnhandledMessageCallback = (
  licIdentifier: string,
  topic: string,
  message: Buffer,
  referenceDate: Date
) => void;
export interface ICodecTransport {
  sendMessage(message: Buffer): Promise<void>;
  onMessage(
    callback: CodecTransportMessageCallback
  ): CodecTransportStopCallback;
}

export interface ICodecTransportFactory {
  get onUnhandledMessage(): CodecTransportUnhandledMessageCallback;
  createTransport(licIdentifier: string): ICodecTransport;
}
