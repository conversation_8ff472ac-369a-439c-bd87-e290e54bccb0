export function date(date: null): null;
export function date(date: undefined): undefined;
export function date(date: string | Date | number): Date;
export function date(
  date: string | Date | number | null | undefined
): Date | null | undefined;
export function date(
  date: string | Date | number | null | undefined
): Date | null | undefined {
  if (date === null) {
    return null;
  }
  if (date === undefined) {
    return undefined;
  }
  if (typeof date === "string") {
    return new Date(date);
  }
  if (typeof date === "number") {
    return new Date(date);
  }
  return date;
}

export function extractHHMM(str: string): { hours: number; minutes: number } {
  const match = str.match(/(\d{1,2}):(\d{2})/);
  if (!match) {
    throw new Error("Invalid time format: " + str);
  }
  const hours = parseInt(match[1]!, 10);
  const minutes = parseInt(match[2]!, 10);
  return { hours, minutes };
}
