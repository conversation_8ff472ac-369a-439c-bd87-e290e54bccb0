import type { S<PERSON> } from "bun";
import { Logger } from "../log";
import { CodecManager } from "./codec-manager";
import type { ICodecTransportFactory } from "../transport/types";
import EventEmitter from "events";
import { listLICS } from "../db/queries/lic-queries";

export class CodecManagerRegistry {
  private static readonly log = Logger.getLogger("CodecManagerRegistry");
  private managers: Map<string, Promise<CodecManager>> = new Map();
  private emitter = new EventEmitter<{
    codecManagerLoadSuccess: [codecManager: CodecManager];
    codecManagerLoadError: [
      error: Error,
      licIdentifier: string,
      referenceDate: Date
    ];
  }>();
  async init(
    db: SQL,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ) {
    CodecManagerRegistry.log.info("CodecManagerRegistry initialized");
    const lics = await listLICS(db);
    CodecManagerRegistry.log.info(`Found ${lics.length} LICs`);
    for (const lic of lics) {
      this.get(db, lic.identifier, transportFactory, referenceDate);
    }
  }

  async get(
    db: SQL,
    licIdentifier: string,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ): Promise<CodecManager> {
    const key = licIdentifier;
    if (!this.managers.has(key)) {
      CodecManagerRegistry.log.info(`Creating new CodecManager for ${key}`);
      const promise = CodecManager.loadFromDB(
        db,
        licIdentifier,
        transportFactory,
        referenceDate
      )
        .then((r) => {
          if (!r) {
            this.managers.delete(key);
            throw new Error("Failed to load CodecManager: " + key);
          }
          CodecManagerRegistry.log.info(`Loaded CodecManager for ${key}`);
          this.emitter.emit("codecManagerLoadSuccess", r);
          return r;
        })
        .catch((err) => {
          this.managers.delete(key);
          this.emitter.emit("codecManagerLoadError", err, key, referenceDate);
          throw err;
        });
      this.managers.set(key, promise);
    }
    return await this.managers.get(key)!;
  }
}
