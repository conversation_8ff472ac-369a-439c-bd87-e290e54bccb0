/**
 * Validation Utilities for IrrigaNet Database Loaders
 *
 * This module contains validation functions and error handling utilities
 * for the database loading system. Following the Single Responsibility
 * principle, all validation logic is centralized here.
 */

import type { ProjectWithSchedulingData } from "../../db/queries/types";
import type { LICTree, ValidPumpModel } from "./types";
import { VALID_PUMP_MODELS } from "./types";
import { isDefined } from "./utilities";

// ==============================================
// VALIDATION ERROR CLASSES
// ==============================================

/**
 * Base class for validation errors in the database loading system
 */
export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly context?: Record<string, unknown>
  ) {
    super(message);
    this.name = "ValidationError";
  }
}

/**
 * Error thrown when a required property is missing
 */
export class MissingPropertyError extends ValidationError {
  constructor(propertyName: string, entityType: string, entityId?: string) {
    const message = entityId
      ? `Missing required property '${propertyName}' in ${entityType} '${entityId}'`
      : `Missing required property '${propertyName}' in ${entityType}`;
    super(message, { propertyName, entityType, entityId });
    this.name = "MissingPropertyError";
  }
}

/**
 * Error thrown when a value is invalid
 */
export class InvalidValueError extends ValidationError {
  constructor(
    propertyName: string,
    actualValue: unknown,
    expectedValues?: unknown[],
    entityType?: string
  ) {
    const expectedStr = expectedValues
      ? ` (expected: ${expectedValues.join(", ")})`
      : "";
    const entityStr = entityType ? ` in ${entityType}` : "";
    const message = `Invalid value '${actualValue}' for property '${propertyName}'${expectedStr}${entityStr}`;
    super(message, { propertyName, actualValue, expectedValues, entityType });
    this.name = "InvalidValueError";
  }
}

// ==============================================
// TREE VALIDATION
// ==============================================

/**
 * Validate that a LIC tree has the required structure
 */
export function validateLICTree(
  tree: unknown,
  licIdentifier: string
): asserts tree is LICTree {
  if (!tree) {
    throw new ValidationError(`No tree found for LIC device ${licIdentifier}`);
  }

  const treeObj = tree as Record<string, unknown>;

  if (!treeObj.propertyDevice) {
    throw new MissingPropertyError(
      "propertyDevice",
      "LIC device",
      licIdentifier
    );
  }

  // Additional tree structure validation can be added here
}

// ==============================================
// PROJECT VALIDATION
// ==============================================

/**
 * Validate that a project has the required irrigation pump configuration
 */
export function validateProjectIrrigationPump(
  project: ProjectWithSchedulingData
): void {
  if (!project.irrigation_water_pump) {
    throw new MissingPropertyError(
      "irrigation_water_pump",
      "project",
      project.id
    );
  }

  if (!project.irrigation_water_pump.water_pump_controller) {
    throw new MissingPropertyError(
      "water_pump_controller",
      "irrigation water pump",
      project.irrigation_water_pump.id
    );
  }
}

/**
 * Validate water pump controller model
 */
export function validatePumpControllerModel(
  model: string | null | undefined,
  identifier: string
): asserts model is ValidPumpModel {
  if (!isDefined(model)) {
    throw new MissingPropertyError(
      "model",
      "water pump controller",
      identifier
    );
  }

  if (!VALID_PUMP_MODELS.includes(model as ValidPumpModel)) {
    throw new InvalidValueError(
      "model",
      model,
      [...VALID_PUMP_MODELS],
      `water pump controller ${identifier}`
    );
  }
}

/**
 * Validate that a project has valid pump configuration
 */
export function validateProjectPumpConfiguration(
  project: ProjectWithSchedulingData
): void {
  validateProjectIrrigationPump(project);

  const pumpController = project.irrigation_water_pump.water_pump_controller;
  validatePumpControllerModel(pumpController.model, pumpController.identifier);
}

// ==============================================
// PROPERTY VALIDATION
// ==============================================

/**
 * Validate property exists and has required fields
 */
export function validateProperty(property: unknown, propertyId: string): void {
  if (!property) {
    throw new ValidationError(`Property not found: ${propertyId}`);
  }

  // Additional property validation can be added here
}

// ==============================================
// DEVICE VALIDATION
// ==============================================

/**
 * Validate device identifier is not empty
 */
export function validateDeviceIdentifier(
  identifier: string | null | undefined,
  deviceType: string
): void {
  if (!identifier || identifier.trim().length === 0) {
    throw new MissingPropertyError("identifier", deviceType);
  }
}

/**
 * Validate device has required metadata
 */
export function validateDeviceMetadata(
  metadata: Record<string, unknown> | null | undefined,
  deviceIdentifier: string,
  requiredFields: string[]
): void {
  if (!metadata) {
    return; // Metadata is optional in most cases
  }

  for (const field of requiredFields) {
    if (
      !(field in metadata) ||
      metadata[field] === null ||
      metadata[field] === undefined
    ) {
      throw new MissingPropertyError(
        field,
        `device metadata for ${deviceIdentifier}`
      );
    }
  }
}

// ==============================================
// COLLECTION VALIDATION
// ==============================================

/**
 * Validate that an array is not empty
 */
export function validateNonEmptyArray<T>(
  array: T[] | null | undefined,
  arrayName: string,
  contextId?: string
): asserts array is T[] {
  if (!array || array.length === 0) {
    const context = contextId ? ` for ${contextId}` : "";
    throw new ValidationError(`${arrayName} cannot be empty${context}`);
  }
}

/**
 * Validate that all items in an array pass a validation function
 */
export function validateArrayItems<T>(
  array: T[],
  validator: (item: T, index: number) => void,
  arrayName: string
): void {
  array.forEach((item, index) => {
    try {
      validator(item, index);
    } catch (error) {
      if (error instanceof ValidationError) {
        throw new ValidationError(
          `Validation failed for ${arrayName}[${index}]: ${error.message}`,
          { originalError: error, arrayName, index }
        );
      }
      throw error;
    }
  });
}

// ==============================================
// UTILITY VALIDATION FUNCTIONS
// ==============================================

/**
 * Validate that a value is within a numeric range
 */
export function validateNumericRange(
  value: number,
  min: number,
  max: number,
  propertyName: string
): void {
  if (value < min || value > max) {
    throw new InvalidValueError(propertyName, value, [`${min} to ${max}`]);
  }
}

/**
 * Validate that a string matches a pattern
 */
export function validateStringPattern(
  value: string,
  pattern: RegExp,
  propertyName: string,
  patternDescription?: string
): void {
  if (!pattern.test(value)) {
    const description = patternDescription ? ` (${patternDescription})` : "";
    throw new InvalidValueError(propertyName, value, [
      `pattern: ${pattern}${description}`,
    ]);
  }
}
