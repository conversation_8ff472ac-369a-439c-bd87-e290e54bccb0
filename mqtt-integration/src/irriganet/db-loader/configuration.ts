/**
 * Configuration Generation for IrrigaNet Database Loaders
 *
 * This module handles the generation of codec configurations and LIC configurations.
 * Following the Single Responsibility principle, all configuration-related logic
 * is centralized here.
 */

import { codec } from "proto";
import type {
  Property,
  PropertyDeviceWithDevice,
  PropertyDeviceWithDeviceAndProperty,
} from "../../db/queries/types";
import type { CodecConfig, IrriganetCodecConfig } from "./types";
import { DEFAULT_VALUES } from "./types";
import { calculateRainGaugeFactor, hoursToMinutes } from "./utilities";

// ==============================================
// CODEC CONFIGURATION
// ==============================================

export function createCodecConfig(
  idx: number,
  propertyDevice: PropertyDeviceWithDevice,
  property: Property
): CodecConfig;
export function createCodecConfig(
  idx: number,
  propertyDevice: PropertyDeviceWithDeviceAndProperty
): CodecConfig;
/**
 * Create a codec configuration object from LIC and property data
 *
 * @param idx - Index of the codec in the property's LIC list
 * @param propertyDevice - LIC device with property device information
 * @param property - Property containing configuration parameters
 * @returns Complete codec configuration
 */
export function createCodecConfig(
  idx: number,
  propertyDevice:
    | PropertyDeviceWithDevice
    | PropertyDeviceWithDeviceAndProperty,
  property?: Property
): CodecConfig {
  return {
    idx,
    enabled: 1,
    identity: propertyDevice.device.identifier,
    name: propertyDevice.metadata?.label || propertyDevice.device.identifier,
    propertyDeviceId: propertyDevice.id,
    config: property
      ? generateLICConfig(propertyDevice as PropertyDeviceWithDevice, property)
      : generateLICConfig(
          propertyDevice as PropertyDeviceWithDeviceAndProperty
        ),
  };
}

// ==============================================
// LIC CONFIGURATION GENERATION
// ==============================================

export function generateLICConfig(
  propertyDevice: PropertyDeviceWithDevice,
  property: Property
): IrriganetCodecConfig;
export function generateLICConfig(
  propertyDevice: PropertyDeviceWithDeviceAndProperty
): IrriganetCodecConfig;
/**
 * Generate LIC configuration from property and device data
 *
 * @param property - Property containing system configuration
 * @param propertyDevice - Property device containing device-specific metadata
 * @returns Complete LIC configuration
 */
export function generateLICConfig(
  propertyDevice:
    | PropertyDeviceWithDevice
    | PropertyDeviceWithDeviceAndProperty,
  property?: Property
): IrriganetCodecConfig {
  const wifiConfig = generateWifiConfig(propertyDevice);
  property = property || (propertyDevice.property as Property);
  return {
    backwashCycle: property.backwash_period_minutes || 0,
    backwashDuration: property.backwash_duration_minutes || 0,
    backwashDelay:
      property.backwash_delay_seconds || DEFAULT_VALUES.BACKWASH_DELAY_SECONDS,
    raingaugeEnabled: property.rain_gauge_enabled || false,
    raingaugeFactor: calculateRainGaugeFactor(
      property.rain_gauge_resolution_mm
    ),
    rainfallLimit:
      property.precipitation_volume_limit_mm ||
      DEFAULT_VALUES.RAINFALL_LIMIT_MM,
    rainfallPauseDuration: calculateRainfallPauseDuration(
      property.precipitation_suspended_duration_hours
    ),
    wifi: wifiConfig,
    // Mesh configuration is not populated in the new system
    publishRawData: DEFAULT_VALUES.PUBLISH_RAW_DATA,
    debug: DEFAULT_VALUES.DEBUG,
    enableScheduleResumption: DEFAULT_VALUES.ENABLE_SCHEDULE_RESUMPTION,
    enableFertiResumption: DEFAULT_VALUES.ENABLE_FERTI_RESUMPTION,
    maxResumptionAttempts: DEFAULT_VALUES.MAX_RESUMPTION_ATTEMPTS,
  };
}

// ==============================================
// WIFI CONFIGURATION
// ==============================================

/**
 * Generate WiFi configuration from property device metadata
 *
 * For LIC devices, WiFi credentials are retrieved from property_device metadata
 * instead of device metadata.
 *
 * @param propertyDevice - Property device containing WiFi metadata
 * @returns WiFi configuration or undefined if not available
 */
export function generateWifiConfig(
  propertyDevice: PropertyDeviceWithDevice | PropertyDeviceWithDeviceAndProperty
): codec.in_.config.WifiConfig | undefined {
  const metadata = propertyDevice.metadata;

  if (!metadata?.wifiSSID || !metadata?.wifiPassword) {
    return undefined;
  }

  return codec.in_.config.WifiConfig.create({
    ssid: metadata.wifiSSID,
    password: metadata.wifiPassword,
  });
}

// ==============================================
// CONFIGURATION CALCULATION UTILITIES
// ==============================================

/**
 * Calculate rainfall pause duration in minutes
 *
 * @param hours - Duration in hours from property configuration
 * @returns Duration in minutes, with default fallback
 */
function calculateRainfallPauseDuration(
  hours: number | null | undefined
): number {
  if (!hours || hours <= 0) {
    return DEFAULT_VALUES.RAINFALL_PAUSE_DURATION_MINUTES;
  }
  return hoursToMinutes(hours);
}

// ==============================================
// CONFIGURATION VALIDATION
// ==============================================

/**
 * Validate that a codec configuration has all required fields
 *
 * @param config - Configuration to validate
 * @throws Error if configuration is invalid
 */
export function validateCodecConfig(config: CodecConfig): void {
  if (!config.identity || config.identity.trim().length === 0) {
    throw new Error("Codec configuration must have a valid identity");
  }

  if (!config.name || config.name.trim().length === 0) {
    throw new Error("Codec configuration must have a valid name");
  }

  if (!config.propertyDeviceId || config.propertyDeviceId.trim().length === 0) {
    throw new Error("Codec configuration must have a valid propertyDeviceId");
  }

  if (config.idx < 1) {
    throw new Error("Codec configuration idx must be greater than 0");
  }
}

/**
 * Validate that a LIC configuration has valid values
 *
 * @param config - LIC configuration to validate
 * @throws Error if configuration is invalid
 */
export function validateLICConfig(config: IrriganetCodecConfig): void {
  if ((config.backwashCycle || 0) < 0) {
    throw new Error("Backwash cycle must be non-negative");
  }

  if ((config.backwashDuration || 0) < 0) {
    throw new Error("Backwash duration must be non-negative");
  }

  if ((config.backwashDelay || 0) < 0) {
    throw new Error("Backwash delay must be non-negative");
  }

  if ((config.raingaugeFactor || 0) <= 0) {
    throw new Error("Rain gauge factor must be positive");
  }

  if ((config.rainfallLimit || 0) < 0) {
    throw new Error("Rainfall limit must be non-negative");
  }

  if ((config.rainfallPauseDuration || 0) <= 0) {
    throw new Error("Rainfall pause duration must be positive");
  }
}

// ==============================================
// CONFIGURATION UTILITIES
// ==============================================

/**
 * Create a default LIC configuration with standard values
 *
 * @returns Default LIC configuration
 */
export function createDefaultLICConfig(): IrriganetCodecConfig {
  return {
    backwashCycle: 0,
    backwashDuration: 0,
    backwashDelay: DEFAULT_VALUES.BACKWASH_DELAY_SECONDS,
    raingaugeEnabled: false,
    raingaugeFactor: DEFAULT_VALUES.RAIN_GAUGE_FACTOR,
    rainfallLimit: DEFAULT_VALUES.RAINFALL_LIMIT_MM,
    rainfallPauseDuration: DEFAULT_VALUES.RAINFALL_PAUSE_DURATION_MINUTES,
    wifi: undefined,
    publishRawData: DEFAULT_VALUES.PUBLISH_RAW_DATA,
    debug: DEFAULT_VALUES.DEBUG,
    enableScheduleResumption: DEFAULT_VALUES.ENABLE_SCHEDULE_RESUMPTION,
    enableFertiResumption: DEFAULT_VALUES.ENABLE_FERTI_RESUMPTION,
    maxResumptionAttempts: DEFAULT_VALUES.MAX_RESUMPTION_ATTEMPTS,
  };
}

/**
 * Merge partial configuration with defaults
 *
 * @param partial - Partial configuration to merge
 * @returns Complete configuration with defaults applied
 */
export function mergeWithDefaults(
  partial: Partial<IrriganetCodecConfig>
): IrriganetCodecConfig {
  const defaults = createDefaultLICConfig();
  return { ...defaults, ...partial };
}
