/**
 * IrrigaNet Database Types
 *
 * This file contains TypeScript interfaces and type definitions that correspond
 * to the SQLite database schema used in the IrrigaNet irrigation management system.
 * These types are designed for use with MQTT integration and data synchronization
 * between the Android application and external systems.
 */

import { codec } from "proto";
import type { getLICTree } from "../../db/queries/lic-queries";
import type { OmitAndMerge } from "../../db/queries/types";

// ==============================================
// CORE DATABASE TYPES
// ==============================================

/**
 * Base interface for all database entities with an auto-incrementing primary key
 */
interface DatabaseEntity {
  idx: number;
}

/**
 * Base interface for entities that have an order index for sequencing
 */
interface OrderIndexedEntity extends DatabaseEntity {
  ord_idx: number;
}

// ==============================================
// CODEC TABLE TYPES
// ==============================================

/**
 * Represents a codec (communication hub) that controls irrigation systems.
 * Codecs are the main communication hubs that manage multiple groups of devices
 * and maintain WiFi connectivity for the irrigation network.
 */
export interface IrriganetCodec extends DatabaseEntity {
  identity: string; // Unique identifier for the codec device
  name: string; // Human-readable name for the codec
  wifi_ssid?: string; // WiFi network name for codec connectivity
  wifi_passwd?: string; // WiFi password for network access
  last_devices_update?: number; // Timestamp of last devices synchronization
  last_scheduling_update?: number; // Timestamp of last scheduling synchronization
  last_device_scheduling_update?: number; // Timestamp of last device scheduling sync
  last_automation_update?: number; // Timestamp of last automation sync
  last_config_update?: number; // Timestamp of last configuration sync
  enabled: number; // Flag to enable/disable the codec (1=enabled, 0=disabled)
}

// ==============================================
// GROUPS TABLE TYPES
// ==============================================

/**
 * Represents a logical group of mesh devices under a single codec.
 * Groups provide an organizational layer between codecs and devices,
 * allowing devices to be organized by location, function, or management needs.
 */
export interface IrriganetGroup extends DatabaseEntity {
  name: string; // Human-readable name for the group
  codec_idx?: number; // Foreign key reference to the parent codec
}

// ==============================================
// MESH DEVICES TABLE TYPES
// ==============================================

/**
 * Represents a physical mesh network device that can contain multiple individual devices.
 * Mesh devices are hardware units deployed in the field that act as intermediaries
 * between the codec and individual control devices.
 */
export interface IrriganetMeshDevice extends DatabaseEntity {
  identity: string; // Unique identifier for the mesh device
  name: string; // Human-readable name for the mesh device
  type?: number; // Device type classification (0=standard, other values for specialized types)
  mode?: number; // Operating mode of the device
  equipament?: number; // Equipment type identifier
  check_input?: number; // Flag for input monitoring capability
  devices_bitmask?: number; // Bitmask representing connected sub-devices
  level_pump_idx?: number; // Database ID of device to use as level pump
  level_pump_enable?: number; // Whether level pump functionality is enabled
  level_pump_working_time?: number; // Working time for level pump operations
  codec_idx?: number; // Foreign key to parent codec
  group_idx?: number; // Foreign key to parent group
}

// ==============================================
// DEVICES TABLE TYPES
// ==============================================

/**
 * Represents an individual controllable component like valves, pumps, or sensors.
 * Multiple devices can exist within a single mesh device and represent the
 * actual irrigation control elements.
 */
export interface IrriganetDevice extends OrderIndexedEntity {
  mesh_idx: number; // Foreign key to parent mesh device
  identity: string; // Unique identifier within the mesh device
  type: number; // Device type (0=sector/valve, 1=pump, 2=fertilizer, etc.)
  out1?: number; // Output channel 1 configuration
  out2?: number; // Output channel 2 configuration
  input?: number; // Input channel configuration
  mode?: number; // Operating mode
  sector?: number; // Sector number for irrigation zones
  power?: number; // Power configuration
  eqpt_ver?: number; // Equipment version identifier
}

// ==============================================
// SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents an irrigation scheduling configuration.
 * Schedulings define when and how irrigation should occur, including timing,
 * fertilization, and backwash configurations for specific groups.
 */
export interface IrriganetScheduling extends OrderIndexedEntity {
  group_idx: number; // Foreign key to parent group
  name: string; // Human-readable name for the schedule
  hour: number; // Start hour (0-23, 24-hour format)
  min: number; // Start minute (0-59)
  start_time: number; // Start time in minutes from midnight
  end_time?: number; // Optional end time constraint in minutes from midnight
  days_of_week: number; // Bitmask for days (bit 0=Sunday, bit 1=Monday, etc.)
  number_of_steps: number; // Number of irrigation steps/phases
  allow_ferti: number; // Whether fertilizer injection is allowed (1=allowed, 0=not allowed)
  allow_backwash: number; // Whether backwash operation is allowed (1=allowed, 0=not allowed)
  waterpump_idx?: number; // Database ID of water pump device
  waterpump_ord_idx?: number; // Order index of water pump device
  waterpump_working_time?: number; // Working time for water pump operations
  ferti_idx?: number; // Database ID of fertilizer device
  ferti_ord_idx?: number; // Order index of fertilizer device
  backwash_idx?: number; // Database ID of backwash device
  backwash_ord_idx?: number; // Order index of backwash device
  once?: number; // Whether this is a one-time schedule (1=once, 0=recurring)
  enabled: number; // Whether this schedule is active (1=active, 0=inactive)
}

// ==============================================
// SECTOR SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents sector-specific scheduling details within a scheduling.
 * Sector schedulings define which sectors (irrigation zones) are activated
 * during specific scheduling periods and their operational parameters.
 */
export interface IrriganetSectorScheduling extends DatabaseEntity {
  scheduling_idx: number; // Foreign key to parent scheduling
  device_idx: number; // Foreign key to the controlled device
  n_order: number; // Execution order within the schedule
  enabled: number; // Whether this sector scheduling is active (1=active, 0=inactive)
  type: number; // Type of operation
  ferti: number; // Fertilizer injection flag
  ferti_delay: number; // Delay before fertilizer injection (seconds)
  working_time: number; // Duration of operation (seconds)
}

// ==============================================
// DEVICE SCHEDULINGS TABLE TYPES
// ==============================================

/**
 * Represents detailed scheduling assignments for individual devices.
 * Device schedulings store the operational sequence and parameters for
 * individual devices during scheduled irrigation periods.
 */
export interface IrriganetDeviceScheduling extends OrderIndexedEntity {
  scheduling_idx: number; // Foreign key to parent scheduling
  device_idx: number; // Foreign key to the controlled device
  n_order: number; // Execution order within the schedule
  status: number; // Current operational status
  type: number; // Type of device operation
  time: number; // Scheduled execution time
  sector_working_time: number; // Duration for sector operation (seconds)
  ferti_working_time?: number; // Duration for fertilizer operation (seconds)
  ferti_delay: number; // Delay before fertilizer injection (seconds)
}

// ==============================================
// CODEC CONFIGURATION
// ==============================================

/**
 * Although it is not a SQLite table, it is a important structure.
 * In the irriganet Android app it is mostly stored in SharedPreferences.
 */
export interface IrriganetCodecConfig extends codec.in_.config.IConfigPackage {}

/**
 * Constants and Type Definitions for IrrigaNet Database Loaders
 *
 * This module contains all constants, enums, and internal type definitions
 * used throughout the database loading system. Following the KISS principle,
 * all constants are centralized here for easy maintenance and consistency.
 */

// ==============================================
// DEVICE TYPE CONSTANTS
// ==============================================

/**
 * Mesh device type constants used for device classification
 */
export const MESH_DEVICE_TYPES = {
  Valve: 0,
  Pump: 1,
  Level: 2,
} as const;

/**
 * Device type constants for individual devices within mesh devices
 */
export const DEV_TYPES = {
  Valve: 0,
  IrrigationPump: 1,
  Ferti: 2,
  Backwash: 3,
  ServicePump: 4,
  Level: 5,
} as const;

export const CONTROL_MSG_ACTIONS = {
  NONE: codec.in_.control.MsgAction.MSG_NONE,
  TURN_ON: codec.in_.control.MsgAction.MSG_TURN_ON,
  TURN_OFF: codec.in_.control.MsgAction.MSG_TURN_OFF,
  PACKAGE: codec.in_.control.MsgAction.MSG_PACKAGE,
} as const;

export type ControlMsgAction = keyof typeof CONTROL_MSG_ACTIONS;

export const COMMAND_MSG_TYPES = {
  RESUME: codec.in_.command.MsgType.MSG_RESUME,
  PAUSE: codec.in_.command.MsgType.MSG_PAUSE,
} as const;

export type CommandMsgType = keyof typeof COMMAND_MSG_TYPES;

/**
 * Days of week bitmask constants
 */
export const DAYS_OF_WEEK = {
  SUN: 1, // Bit 0
  MON: 2, // Bit 1
  TUE: 4, // Bit 2
  WED: 8, // Bit 3
  THU: 16, // Bit 4
  FRI: 32, // Bit 5
  SAT: 64, // Bit 6
} as const;

export type DaysOfWeek = keyof typeof DAYS_OF_WEEK;

// ==============================================
// DEVICE MODE CONSTANTS
// ==============================================

/**
 * Device mode flags for pulse and monitoring operations
 */
export const PULSE = 0x01 as const;
export const MONI = 0x02 as const;

// ==============================================
// INTERNAL TYPE DEFINITIONS
// ==============================================

/**
 * Raw LIC tree type from database query
 */
export type LICTreeRaw = NonNullable<Awaited<ReturnType<typeof getLICTree>>>;

/**
 * Validated LIC tree with guaranteed propertyDevice
 */
export type LICTree = OmitAndMerge<
  LICTreeRaw,
  "propertyDevice",
  { propertyDevice: NonNullable<LICTreeRaw["propertyDevice"]> }
>;

/**
 * Codec configuration with additional metadata
 */
export type CodecConfig = IrriganetCodec & {
  propertyDeviceId: string;
  config: IrriganetCodecConfig;
};

/**
 * Group with associated project ID for internal processing
 */
export type IrriganetGroupWithProjectId = IrriganetGroup & {
  projectId: string;
};

/**
 * Mesh device with associated device ID for internal processing
 */
export type IrriganetMeshDeviceWithDeviceId = IrriganetMeshDevice & {
  deviceId: string;
};

export type IrriganetDeviceWithElementId = IrriganetDevice & {
  elementType: "valve" | "pump" | "reservoir" | undefined;
  elementId: string | undefined;
};

/**
 * Scheduling with associated irrigation plan ID for internal processing
 */
export type IrriganetSchedulingWithIrrigationPlanId = IrriganetScheduling & {
  irrigationPlanId: string;
};

export type IrriganetSectorSchedulingWithStepId = IrriganetSectorScheduling & {
  irrigationPlanStepId: string;
};

/**
 * Represents the state of the LIC (Local Irrigation Controller)
 * Corresponds to the irriganet android sqlite database structure
 */
export type LICState = {
  /**
   * Corresponds to the irriganet android "Codecs" sqlite table plus some sharedPrefs configuration
   */
  lic: CodecConfig & { irrigaMaisDeviceId: string };
  /**
   * Corresponds to the irriganet android "groups" sqlite table
   */
  groups: IrriganetGroupWithProjectId[];
  /**
   * Corresponds to the irriganet android "devices" sqlite table
   */
  devices: IrriganetDeviceWithElementId[];
  /**
   * Corresponds to the irriganet android "mesh_devices" sqlite table
   */
  meshDevices: IrriganetMeshDeviceWithDeviceId[];
  /**
   * Corresponds to the irriganet android "Schedulings" sqlite table
   */
  schedules: IrriganetSchedulingWithIrrigationPlanId[];
  /**
   * Corresponds to the irriganet android "Sector_Schedulings" sqlite table
   */
  sectorSchedules: IrriganetSectorSchedulingWithStepId[];
  /**
   * Corresponds to the irriganet android "Device_Schedulings" sqlite table
   */
  deviceSchedules: IrriganetDeviceScheduling[];
};

// ==============================================
// TYPE GUARDS AND VALIDATION TYPES
// ==============================================

/**
 * Valid water pump controller models
 */
export const VALID_PUMP_MODELS = ["WPC-PL10", "WPC-PL50"] as const;
export type ValidPumpModel = (typeof VALID_PUMP_MODELS)[number];

/**
 * Valid backwash pump types
 */
export const VALID_BACKWASH_TYPES = ["IRRIGATION", "FERTIGATION"] as const;
export type ValidBackwashType = (typeof VALID_BACKWASH_TYPES)[number];

/**
 * Valid pump modes
 */
export const VALID_PUMP_MODES = ["CONTINUOUS", "PULSE"] as const;
export type ValidPumpMode = (typeof VALID_PUMP_MODES)[number];

// ==============================================
// DEFAULT VALUES
// ==============================================

/**
 * Default configuration values for various system parameters
 */
export const DEFAULT_VALUES = {
  BACKWASH_DELAY_SECONDS: 30,
  RAIN_GAUGE_FACTOR: 5, // Default factor for 0.2mm resolution
  RAINFALL_LIMIT_MM: 2,
  RAINFALL_PAUSE_DURATION_MINUTES: 1440, // 24 hours
  PUBLISH_RAW_DATA: false,
  DEBUG: false,
  ENABLE_SCHEDULE_RESUMPTION: false,
  ENABLE_FERTI_RESUMPTION: false,
  MAX_RESUMPTION_ATTEMPTS: 0,
} as const;

/**
 * Helper function to create days of week bitmask
 */
export function createDaysOfWeekBitmask(days: DaysOfWeek[]): number {
  let bitmask = 0;

  for (const day of days) {
    if (DAYS_OF_WEEK[day]) {
      bitmask |= DAYS_OF_WEEK[day];
    }
  }

  return bitmask;
}

/**
 * Helper function to check if a day is included in the bitmask
 */
export function isDayIncluded(bitmask: number, day: DaysOfWeek): boolean {
  return (bitmask & DAYS_OF_WEEK[day]) !== 0;
}

export function isDaysOfWeekArray(arr: string[]): arr is DaysOfWeek[] {
  return arr.every((day) => day in DAYS_OF_WEEK);
}
