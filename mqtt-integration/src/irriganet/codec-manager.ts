import type { SQL } from "bun";
import { codec } from "proto";
import type {
  CodecTransportStopCallback,
  ICodecTransport,
  ICodecTransportFactory,
} from "../transport/types";
import { loadLICStateByIdentifier } from "./db-loader";
import type { LICState } from "./db-loader/types";
import {
  automationPackage,
  commandPacket,
  configPackage,
  controlPackage,
  deviceSchedulingPackage,
  devicesPackage,
  firmwareUpdatePackage,
  requestInfoPackage,
  schedulingPackage,
} from "./proto";
import { Logger, logger } from "../log";
import { appendCRC16 } from "./db-loader/utilities";
import { getLIC } from "../db/queries/lic-queries";
import { insertLIC } from "../db/mutations/lic";
import db from "../db/connection";
import { insertCurrentLICPacket } from "../db/mutations/current-lic-packet";
import fastq from "fastq";
import type { CurrentLICPacketInsert } from "../db/mutations/types";

const insertPacketQueue = fastq<unknown, CurrentLICPacketInsert, number>(
  async (task, cb) => {
    await insertCurrentLICPacket(db, task)
      .then((r) => {
        const err = r?.id
          ? null
          : new Error("Failed to insert current LIC packet");
        cb(err, r?.id);
      })
      .catch((err) => {
        cb(err);
      });
  },
  1
); // Limit concurrency to 1

function isJSONPayload(payload: Buffer): boolean {
  return payload.at(0) === 123 && payload.at(-1) === 125; // Check if payload starts with '{' and ends with '}'
}

const parseMessageLogger = Logger.getLogger("CodecManager.parseMessage");
function parseMessage(topic: string, message: Buffer, referenceDate: Date) {
  if (isJSONPayload(message)) {
    try {
      const jsonPayload = JSON.parse(message.toString());
      return { jsonPayload };
    } catch (error) {
      parseMessageLogger.error("Failed to decode JSON report message:", error);
    }
    return null;
  }
  try {
    if (topic === "report") {
      const decoded = codec.out.OutgoingPacket.decode(message);
      if (parseMessageLogger.traceEnabled) {
        parseMessageLogger.debug(
          "Decoded report message:",
          `payload=${decoded.payload}`,
          JSON.stringify(decoded, null, 2)
        );
      }
      return decoded;
    }
  } catch (error) {
    parseMessageLogger.error("Failed to decode report message:", error);
  }
  return null;
}
// "config" | "devices" | "scheduling" | "devScheduling" | "automation" | "control" | "command" | "requestInfo" | "firmwareUpdate"
const packetBuilders = {
  config: configPackage,
  devices: devicesPackage,
  scheduling: schedulingPackage,
  devScheduling: deviceSchedulingPackage,
  automation: automationPackage,
  requestInfo: requestInfoPackage,
  firmwareUpdate: firmwareUpdatePackage,
  control: controlPackage,
  command: commandPacket,
} as const;

type PacketBuilders = typeof packetBuilders;
type Tail<T extends any[]> = T extends [any, ...infer R] ? R : never;
type TailArgs<F extends (...a: any[]) => any> = Tail<Parameters<F>>;
type FirstArg<F extends (...a: any[]) => any> = Parameters<F>[0];
type FullArgs<K extends keyof PacketBuilders> = Parameters<PacketBuilders[K]>;

const log = Logger.getLogger("CodecManager");
export class CodecManager {
  private readonly stopCallback: CodecTransportStopCallback;
  constructor(
    private licIdentifier: string,
    private referenceDate: Date,
    private state: LICState,
    private transport: ICodecTransport
  ) {
    this.stopCallback = this.transport.onMessage(
      async (topic, payload, referenceDate) => {
        await this.handleMessage(topic, payload, referenceDate);
      }
    );
  }

  getState(): LICState {
    return this.state;
  }

  getLicIdentifier() {
    return this.licIdentifier;
  }

  getReferenceDate() {
    return this.referenceDate;
  }

  async handleMessage(topic: string, message: Buffer, referenceDate: Date) {
    // Handle the incoming message

    log.debug(
      `[${this.licIdentifier}] Received report message on topic ${topic}:`,
      message
    );
    const parsed = parseMessage(topic, message, referenceDate);
    if (parsed) {
      const payloadType =
        "jsonPayload" in parsed ? "jsonPayload" : parsed.payload;
      const payloadData =
        "jsonPayload" in parsed ? parsed.jsonPayload : parsed.toJSON();
      if (log.traceEnabled) {
        log.debug(
          `[${this.licIdentifier}] Parsed message ${payloadType} on topic ${topic}:`,
          payloadData
        );
      }
      insertPacketQueue.push(
        {
          device: this.state.lic.irrigaMaisDeviceId,
          packet_date: referenceDate,
          payload_type: payloadType ?? "unknown",
          payload_data: payloadData,
        },
        (err, r) => {
          if (err) {
            log.error(
              `[${this.licIdentifier}] Failed to insert LIC packet for device ${this.state.lic.irrigaMaisDeviceId}:`,
              err
            );
          } else {
            log.debug(
              `[${this.licIdentifier}] Inserted LIC packet for device ${this.state.lic.irrigaMaisDeviceId} with id=${r}`
            );
          }
        }
      );
    } else {
      log.error(
        `[${this.licIdentifier}] Failed to parse message on topic ${topic}:`,
        message
      );
    }
  }

  send<K extends NonNullable<codec.in_.IncomingPacket["payload"]>>(
    messageType: K,
    id: Date | number,
    ...params: TailArgs<PacketBuilders[K]>
  ) {
    id = typeof id === "number" ? id : Math.round(id.getTime() / 1000);
    const packet = this.createIncomingPacket(messageType, id, ...params);
    this.sendPacket(packet);
  }

  private sendPacket(packet: codec.in_.IIncomingPacket) {
    const payload = appendCRC16(
      codec.in_.IncomingPacket.encode(packet).finish()
    );
    this.transport.sendMessage(Buffer.from(payload));
  }

  async reloadFromDB(db: SQL, referenceDate: Date) {
    this.referenceDate = referenceDate;
    await loadLICStateByIdentifier(db, this.licIdentifier, referenceDate).then(
      (state) => {
        if (!state) {
          log.error(`[${this.licIdentifier}] Failed to load LIC state`);
          return null;
        }
        this.state = state;
      }
    );
  }

  private createIncomingPacket<K extends keyof PacketBuilders>(
    messageType: K,
    id: number,
    ...params: TailArgs<PacketBuilders[K]>
  ): codec.in_.IIncomingPacket {
    const fn = packetBuilders[messageType] as unknown as (
      ...a: FullArgs<K>
    ) => ReturnType<PacketBuilders[K]>;

    const args = [this.state, ...params] as unknown as FullArgs<K>;

    const innerData = fn(...args);

    return {
      id,
      [messageType]: innerData,
    } as codec.in_.IIncomingPacket;
  }

  static async loadFromDB(
    db: SQL,
    licIdentifier: string,
    transportFactory: ICodecTransportFactory,
    referenceDate: Date
  ) {
    let state = await loadLICStateByIdentifier(
      db,
      licIdentifier,
      referenceDate
    );
    if (!state) {
      let lic = await getLIC(db, licIdentifier);
      if (!lic) {
        lic = await insertLIC(db, licIdentifier);
        if (!lic) {
          logger.error(`[${licIdentifier}] Failed to create LIC`);
          return null;
        }
      }
      state = {
        devices: [],
        deviceSchedules: [],
        groups: [],
        meshDevices: [],
        schedules: [],
        sectorSchedules: [],
        lic: {
          irrigaMaisDeviceId: lic.id,
          enabled: 1,
          identity: lic.identifier,
          idx: 1,
          name: lic.identifier,
          propertyDeviceId: "", // TODO: make propertyDeviceId optional
          config: {},
        },
      };
    }
    return new CodecManager(
      licIdentifier,
      referenceDate,
      state,
      transportFactory.createTransport(licIdentifier)
    );
  }
}
