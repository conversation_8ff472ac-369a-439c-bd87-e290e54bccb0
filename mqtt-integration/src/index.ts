import db from "./db/connection";
import { CodecManagerRegistry } from "./irriganet/codec-manager-registry";
import { logger } from "./log";
import { createMQTTClient } from "./transport/mqtt/mqtt-client";
import { MQTTTransportFactory } from "./transport/mqtt/mqtt-transport-factory";

const client = createMQTTClient();

const codecStateRegistry = new CodecManagerRegistry();
const transportFactory = new MQTTTransportFactory(
  client,
  (licIdentifier, topic, message, referenceDate) => {
    logger.log(`Unhandled message for ${licIdentifier} on ${topic}:`, message);
    codecStateRegistry
      .get(db, licIdentifier, transportFactory, referenceDate)
      .then((manager) => {
        // Handle the codec state manager
        manager.handleMessage(topic, message, referenceDate);
      });
  }
);
codecStateRegistry.init(db, transportFactory, new Date());

// client.subscribe("#", (err) => {
//   if (err) {
//     console.error("Failed to subscribe to topics:", err);
//   } else {
//     logger.log("Subscribed to all topics");
//   }
// });

// Handle graceful shutdown
const shutdown = () => {
  logger.log("Disconnecting from MQTT broker...");
  client.end(() => {
    logger.log("Disconnected from MQTT broker");
    process.exit(0);
  });
};

process.on("SIGINT", shutdown);
process.on("SIGTERM", shutdown);
process.on("exit", shutdown);
