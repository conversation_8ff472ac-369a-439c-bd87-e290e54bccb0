const LOG_LEVELS = [
  "trace",
  "debug",
  "info",
  "warn",
  "error",
  "silent",
] as const;
export type LogLevel = (typeof LOG_LEVELS)[number];

export function isLogLevel(value: any): value is LogLevel {
  return LOG_LEVELS.includes(value);
}

const rootLogLevel: LogLevel = isLogLevel(process.env.LOG_LEVEL)
  ? (process.env.LOG_LEVEL as LogLevel)
  : "info";

const childrenLogLevel = process.env.CHILDREN_LOG_LEVEL?.split(",")
  .map(
    (part) =>
      part
        .trim()
        .split(":")
        .map((p) => p.trim()) as [string, LogLevel]
  )
  .reduce((acc, [name, level]) => {
    if (isLogLevel(level)) {
      acc[name] = level;
    }
    return acc;
  }, {} as Record<string, LogLevel>);

export class Logger {
  public static readonly ROOT_LOGGER: Logger = new Logger(
    "",
    undefined,
    rootLogLevel
  );
  public static readonly loggers: Map<string, Logger> = new Map([
    [Logger.ROOT_LOGGER.name, Logger.ROOT_LOGGER],
  ]);

  static {
    if (childrenLogLevel) {
      for (const [name, level] of Object.entries(childrenLogLevel)) {
        Logger.setLoggerLevel(name, level);
      }
    }
  }

  private constructor(
    public readonly name: string,
    public readonly parent: Logger | undefined,
    private level: LogLevel | undefined
  ) {}

  private getEffectiveLogLevel(): LogLevel {
    if (this.level) {
      return this.level;
    }
    return this.parent ? this.parent.getEffectiveLogLevel() : rootLogLevel;
  }

  getLevel(): LogLevel {
    return this.getEffectiveLogLevel();
  }

  setLevel(level: LogLevel | undefined) {
    this.level = level;
  }

  isLevelEnabled(level: LogLevel): boolean {
    return (
      LOG_LEVELS.indexOf(level) >=
      LOG_LEVELS.indexOf(this.getEffectiveLogLevel())
    );
  }
  get traceEnabled(): boolean {
    return this.isLevelEnabled("trace");
  }

  get debugEnabled(): boolean {
    return this.isLevelEnabled("debug");
  }

  get infoEnabled(): boolean {
    return this.isLevelEnabled("info");
  }

  get warnEnabled(): boolean {
    return this.isLevelEnabled("warn");
  }

  get errorEnabled(): boolean {
    return this.isLevelEnabled("error");
  }

  get isSilent(): boolean {
    return this.getEffectiveLogLevel() === "silent";
  }

  private prepareLogEntry(
    message: any,
    level: LogLevel | "assert" | "log"
  ): { formattedMessage: string } {
    if (typeof message !== "string") {
      message = JSON.stringify(message);
    }
    const timestamp = new Date().toISOString();
    const prefix = this.name ? `[${this.name}] - ` : "";
    const formattedMessage = `${prefix}${timestamp} - ${level} - ${message}`;
    return { formattedMessage };
  }

  log(message?: any, ...optionalParams: any[]) {
    this._log("log", { message, optionalParams });
  }

  error(message?: any, ...optionalParams: any[]) {
    this._log("error", { message, optionalParams });
  }

  warn(message?: any, ...optionalParams: any[]) {
    this._log("warn", { message, optionalParams });
  }

  debug(message?: any, ...optionalParams: any[]) {
    this._log("debug", { message, optionalParams });
  }

  info(message?: any, ...optionalParams: any[]) {
    this._log("info", { message, optionalParams });
  }

  trace(message?: any, ...optionalParams: any[]) {
    this._log("trace", { message, optionalParams });
  }

  assert(condition: any, message?: any, ...optionalParams: any[]) {
    // if (!condition) {
    //   const { formattedMessage } = this.prepareLogEntry(message);
    //   console.assert(condition, formattedMessage, ...optionalParams);
    // }
    this._log("assert", { message, optionalParams, condition });
  }

  private _log(
    level: LogLevel | "assert" | "log",
    {
      message,
      optionalParams,
      condition,
    }: { message?: any; optionalParams: any[]; condition?: any }
  ) {
    if (
      level !== "silent" &&
      (level === "assert" || level === "log" || this.isLevelEnabled(level))
    ) {
      const { formattedMessage } = this.prepareLogEntry(message, level);
      if (level === "assert") {
        console.assert(condition, formattedMessage, ...optionalParams);
      } else {
        console[level](formattedMessage, ...optionalParams);
      }
    }
  }

  getChildLogger(childName: string): Logger {
    const key = `${this.name ? `${this.name}.` : ""}${childName}`;
    return Logger.getLogger(key);
  }

  toJSON() {
    return {
      name: this.name,
      level: this.level,
      effectiveLevel: this.getEffectiveLogLevel(),
      parentName: this.parent?.name,
    };
  }

  /**
   * Finds the parent logger of a given child logger by walking up the logger hierarchy.
   * It is assumed that logger names are unique within their hierarchy and are in the format "parent.child".
   * @param childName The name of the child logger.
   * @returns The parent logger, or undefined if not found.
   */
  static findParentLogger(childName: string): Logger {
    const parentName = childName.substring(0, childName.lastIndexOf("."));
    const parent = Logger.loggers.get(parentName);
    return parent ?? Logger.findParentLogger(parentName);
  }

  static getLogger(fullName: string): Logger {
    const key = fullName;
    if (!Logger.loggers.has(key)) {
      const parent = Logger.findParentLogger(key);
      Logger.loggers.set(key, new Logger(fullName, parent, undefined));
    }
    return Logger.loggers.get(key)!;
  }

  static setLoggerLevel(fullName: string, level: LogLevel | undefined) {
    const logger = Logger.getLogger(fullName);
    logger.setLevel(level);
  }

  static get root(): Logger {
    return Logger.ROOT_LOGGER;
  }
}

export const logger = Logger.ROOT_LOGGER;
