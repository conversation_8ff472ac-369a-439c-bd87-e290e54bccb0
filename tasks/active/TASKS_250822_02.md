# Task list info:

- name: 250822_02
- base_branch: develop

---

# Tasks

## Task 1. Create Directus configuration for lic_packet

**Description**
Create Directus configuration migrations for lic_packet table including collection setup, field definitions, permissions, and administrative interface. Configure proper display templates and field validation.
The creation table migration is directus/migrations/20250822C-create-lic-packet-hypertable.js

**Target directories**

- directus (backend)

**Status:** Done

## Task 2. Create a service to process device_message_request queue in mqtt-integration package

**Description**
Create a service that monitors the device_message_request table for pending messages and processes them according to priority, schedule, and dependencies.
The service will have a single responsibility: process the queue. All other concerns (e.g. message building, retries, etc.) will be handled by other services.
It will work as a event emitter of device_message_request of interest. Other services will listen to the events and act accordingly.
The service will not update the device_message_request table. It will only query it and emit events.
The service will be implemented in mqtt-integration/src/db/services/device-message-queue.ts.
It will use pooling pattern to query the database every <INTERVAL> seconds.
The service name will be DeviceMessageQueueService.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 2.1. Create device_message_request database query functions

**Description**
Create database query functions for fetching pending device_message_request records with proper filtering, ordering, and dependency handling. Functions should query messages that are ready to be processed based on status, schedule, and parent message dependencies.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 2.2. Create DeviceMessageQueueService class structure

**Description**
Implement the DeviceMessageQueueService class with EventEmitter inheritance, polling timer mechanism, and basic lifecycle methods (start, stop, destroy). Set up the foundation for the service without the actual message processing logic.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 2.3. Implement message processing and event emission logic

**Description**
Add the core logic to process queried messages and emit appropriate events for each device_message_request. Implement the polling loop that queries the database and emits events for pending messages.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 2.4. Add service configuration and error handling

**Description**
Add configuration options for polling interval, logging integration, and comprehensive error handling for database failures and other edge cases. Ensure the service is robust and production-ready.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

## Task 3. Update CodecManagerRegistry listen to device_message_request events an send the messages to LIC devices in mqtt-integration package

**Description**
Update CodecManagerRegistry to handle device_message_request events emitted by the service created in task 2.
The glue code that connects the DeviceMessageQueueService and CodecManagerRegistry will be implemented in mqtt-integration/src/index.ts, where there is an existing CodecManagerRegistry instance and a DeviceMessageQueueService instance needs to be created.
On device_message_request events, CodecManagerRegistry will call the appropriate CodecManager to send the message to the LIC device by calling the send() method.
CodecManager.send has 3 parameters: - messageType: device_message_request.payload_type is the message type to be sent. - id: device_message_request.packet_id is the packet id. - ...params: device_message_request.payload_data the message parameters. if it is null, it should be ignored.
Before sending the message, CodecManager must update the device_message_request row setting the status to "processing" and "attempts += 1" and payload_bytes to the compiled protobuf binary data.
After sending the message, CodecManager must update the device_message_request depending on the result.

- If the message was sent successfully, the status must be set to "sent" and the sent_at field must be set to the current date. The acknowledged_at field must be set to null
- If the message failed to send, the status must be set to "failed". The last_error field must be set to the error message.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 3.1. Create device_message_request database mutation functions

**Description**
Create database mutation functions to update device_message_request records including status updates, attempt increments, payload_bytes updates, timestamp updates (sent_at, acknowledged_at), and error message logging. These functions will be used by CodecManager during message processing.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 3.2. Enhance CodecManager.send() method for database integration

**Description**
Modify the CodecManager.send() method to accept device_message_request data and handle database updates before and after sending messages. The method should update status to "processing", increment attempts, store payload_bytes, then update final status based on send result.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 3.3. Add event listener methods to CodecManagerRegistry

**Description**
Add methods to CodecManagerRegistry to handle device_message_request events emitted by DeviceMessageQueueService. Implement logic to route messages to appropriate CodecManager instances based on device identifier and coordinate the sending process.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 3.4. Update service initialization in index.ts

**Description**
Create DeviceMessageQueueService instance in mqtt-integration/src/index.ts and wire the event listeners between DeviceMessageQueueService and CodecManagerRegistry. Ensure proper service lifecycle management and initialization order.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending

### Subtask 3.5. Add comprehensive error handling and logging

**Description**
Add proper error handling for all failure scenarios including database update failures, CodecManager lookup failures, and message sending failures. Implement comprehensive logging to track message processing lifecycle and troubleshoot issues.

**Target directories**

- mqtt-integration (backend)

**Status:** Pending
