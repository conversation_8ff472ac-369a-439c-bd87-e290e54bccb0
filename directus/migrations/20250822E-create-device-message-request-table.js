/**
 * Migration to create device_message_request table for outgoing message queue to LIC devices
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create device_message_request table with comprehensive field comments
    await tx.schema.createTable("device_message_request", (table) => {
      // Primary Identification
      table.uuid("id").primary().defaultTo(tx.raw("gen_random_uuid()"))
        .comment("Primary key UUID for the message request");
      
      // Device Relationships
      table.uuid("device").notNullable()
        .comment("Target LIC device UUID that will receive this message")
        .references("id").inTable("device").onUpdate("RESTRICT").onDelete("RESTRICT");
      
      table.uuid("property_device").nullable()
        .comment("Optional property device UUID for mesh-aware routing and device association tracking")
        .references("id").inTable("property_device").onUpdate("RESTRICT").onDelete("RESTRICT");
      
      // Message Content (IncomingPacket structure)
      table.bigInteger("packet_id").notNullable()
        .comment("Unique packet identifier per device for protobuf IncomingPacket.id field correlation");
      
      table.string("payload_type", 50).notNullable()
        .comment("Type of protobuf payload: config, devices, scheduling, dev_scheduling, automation, control, command, request_info, firmware_update");
      
      table.jsonb("payload_data").notNullable()
        .comment("JSON representation of protobuf payload data for debugging and inspection");
      
      table.binary("payload_bytes").nullable()
        .comment("Compiled protobuf binary data ready for MQTT transmission (populated during processing)");
      
      table.string("message_hash", 64).nullable()
        .comment("SHA-256 hash for message deduplication and integrity verification");
      
      // Message Relationships & Correlation
      table.uuid("parent_message_id").nullable()
        .comment("Reference to parent message UUID for sequential operation chains and dependencies")
        .references("id").inTable("device_message_request").onUpdate("RESTRICT").onDelete("SET NULL");
      
      table.uuid("correlation_id").nullable()
        .comment("Correlation UUID for grouping related messages across devices and bulk operations");
      
      // Delivery Control
      table.string("status", 20).notNullable().defaultTo("pending")
        .comment("Message lifecycle status: pending, processing, sent, acknowledged, failed, expired, cancelled");
      
      table.smallint("priority").notNullable().defaultTo(5)
        .comment("Message priority level (1=highest, 10=lowest) for queue processing order");
      
      // Scheduling & Timing
      table.timestamp("scheduled_at", { useTz: true }).notNullable().defaultTo(tx.fn.now())
        .comment("Timestamp when message should be processed (now() = immediate, future = scheduled)");
      
      table.timestamp("expires_at", { useTz: true }).nullable()
        .comment("Message expiration timestamp (NULL = never expires)");
      
      table.timestamp("sent_at", { useTz: true }).nullable()
        .comment("Timestamp when message was successfully transmitted via MQTT");
      
      table.timestamp("acknowledged_at", { useTz: true }).nullable()
        .comment("Timestamp when device acknowledged receipt of the message");
      
      // Retry & Error Handling
      table.smallint("attempts").notNullable().defaultTo(0)
        .comment("Current number of delivery attempts made for this message");
      
      table.smallint("max_attempts").notNullable().defaultTo(3)
        .comment("Maximum number of retry attempts before marking message as failed");
      
      table.integer("retry_delay_seconds").defaultTo(30)
        .comment("Seconds to wait between retry attempts (supports exponential backoff)");
      
      table.text("last_error").nullable()
        .comment("Last error message encountered during message processing or delivery");
      
      // Standard Audit Fields
      table.timestamp("date_created", { useTz: true }).notNullable().defaultTo(tx.raw("CURRENT_TIMESTAMP"))
        .comment("Timestamp when this message request was created");
      
      table.uuid("user_created").nullable()
        .comment("UUID of user who created this message request")
        .references("id").inTable("directus_users").onUpdate("RESTRICT").onDelete("RESTRICT");
      
      table.timestamp("date_updated", { useTz: true }).notNullable().defaultTo(tx.raw("CURRENT_TIMESTAMP"))
        .comment("Timestamp when this message request was last updated");
      
      table.uuid("user_updated").nullable()
        .comment("UUID of user who last updated this message request")
        .references("id").inTable("directus_users").onUpdate("RESTRICT").onDelete("RESTRICT");
      
      table.jsonb("metadata").nullable()
        .comment("MQTT transport details (topic, qos, retain) and flexible metadata storage as JSONB");
      
      table.text("notes").nullable()
        .comment("Human-readable notes and additional context for this message request");
      
      // Unique constraint for packet_id per device
      table.unique(["device", "packet_id"], { indexName: "device_message_request_device_packet_id_unique" });
    });

    // Add table comment
    await tx.raw(`
      COMMENT ON TABLE public.device_message_request IS 
        'Queue system for outgoing protobuf messages to LIC devices via MQTT with comprehensive lifecycle management';
    `);
    
    // Add business logic constraints
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_status_check
      CHECK (status IN ('pending', 'processing', 'sent', 'acknowledged', 'failed', 'expired', 'cancelled'));
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_priority_check
      CHECK (priority >= 1 AND priority <= 10);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_payload_type_check
      CHECK (payload_type IN ('config', 'devices', 'scheduling', 'dev_scheduling', 'automation', 'control', 'command', 'request_info', 'firmware_update'));
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_attempts_check
      CHECK (attempts >= 0 AND attempts <= max_attempts);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_max_attempts_check
      CHECK (max_attempts >= 1 AND max_attempts <= 100);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_retry_delay_check
      CHECK (retry_delay_seconds >= 0);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_timing_check
      CHECK (expires_at IS NULL OR expires_at > scheduled_at);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_sent_after_scheduled_check
      CHECK (sent_at IS NULL OR sent_at >= scheduled_at);
    `);
    
    await tx.raw(`
      ALTER TABLE public.device_message_request
      ADD CONSTRAINT device_message_request_ack_after_sent_check
      CHECK (acknowledged_at IS NULL OR (sent_at IS NOT NULL AND acknowledged_at >= sent_at));
    `);

    // Add the update timestamp trigger
    await tx.raw(`
      CREATE OR REPLACE TRIGGER set_device_message_request_date_updated
        BEFORE UPDATE
        ON public.device_message_request
        FOR EACH ROW
        EXECUTE FUNCTION public.update_timestamp_column();
    `);
  });
}

/**
 * Migration to drop device_message_request table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.raw('DROP TRIGGER IF EXISTS set_device_message_request_date_updated ON public.device_message_request');
    await tx.schema.dropTableIfExists('device_message_request');
  });
}