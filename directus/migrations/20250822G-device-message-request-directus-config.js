/**
 * Migration to configure Directus CMS for device_message_request collection
 * 
 * This migration creates comprehensive Directus configuration for the device message
 * request queue system, including collection setup, field definitions with proper
 * interfaces and validation, relationship mapping, and security permissions.
 * 
 * The configuration provides a complete admin interface for managing the MQTT message
 * queue to LIC devices, with proper field types, display options, and access control.
 * 
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // ========================================================================
    // Collection Configuration
    // ========================================================================
    // Creates the main collection definition with display settings, sorting,
    // and administrative options for the device message request queue.
    await tx.batchInsert("directus_collections", [
      {
        collection: "device_message_request",
        icon: "send",
        note: "Queue for outgoing messages to LIC devices via MQTT. Manages message lifecycle from creation to delivery confirmation.",
        display_template:
          "{{device.identifier}} - {{payload_type}} ({{status}})",
        hidden: false,
        singleton: false,
        translations: null,
        archive_field: null,
        archive_app_filter: true,
        archive_value: null,
        unarchive_value: null,
        sort_field: "date_created",
        accountability: "all",
        color: null,
        item_duplication_fields: null,
        sort: null,
        group: null,
        collapse: "open",
        preview_url: null,
        versioning: false,
      },
    ]);

    // ========================================================================
    // Field Configuration (26 fields)
    // ========================================================================
    // Comprehensive field definitions with proper interfaces, display options,
    // validation rules, and user experience optimizations. Each field is
    // configured for optimal usability in the Directus admin interface.
    await tx.batchInsert("directus_fields", [
      // ====================================================================
      // Primary Key Field
      // ====================================================================
      {
        collection: "device_message_request",
        field: "id",
        special: null,
        interface: "input",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 1,
        width: "full",
        translations: null,
        note: null,
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Device Relationship Fields
      // ====================================================================
      // Target device selection with filtering for LIC devices only
      {
        collection: "device_message_request",
        field: "device",
        special: null,
        interface: "select-dropdown-m2o",
        options: JSON.stringify({
          filter: {
            model: { _eq: "LIC" },
          },
        }),
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{identifier}} ({{model}})",
        }),
        readonly: false,
        hidden: false,
        sort: 2,
        width: "half",
        translations: null,
        note: "Target LIC device for this message",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Optional mesh routing device association
      {
        collection: "device_message_request",
        field: "property_device",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{property.name}} - {{device.identifier}}",
        }),
        readonly: false,
        hidden: false,
        sort: 3,
        width: "half",
        translations: null,
        note: "Optional property device association for mesh-aware routing",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Message Content Fields
      // ====================================================================
      // Unique protobuf packet identifier per device
      {
        collection: "device_message_request",
        field: "packet_id",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "1",
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 4,
        width: "half",
        translations: null,
        note: "Unique packet identifier for protobuf correlation (per device)",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Payload type selection with color-coded labels
      {
        collection: "device_message_request",
        field: "payload_type",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Config", value: "config" },
            { text: "Devices", value: "devices" },
            { text: "Scheduling", value: "scheduling" },
            { text: "Dev Scheduling", value: "dev_scheduling" },
            { text: "Automation", value: "automation" },
            { text: "Control", value: "control" },
            { text: "Command", value: "command" },
            { text: "Request Info", value: "request_info" },
            { text: "Firmware Update", value: "firmware_update" },
          ],
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            { text: "Config", value: "config", foreground: "#FFFFFF", background: "#2563EB" },
            { text: "Devices", value: "devices", foreground: "#FFFFFF", background: "#7C3AED" },
            { text: "Scheduling", value: "scheduling", foreground: "#FFFFFF", background: "#059669" },
            { text: "Dev Scheduling", value: "dev_scheduling", foreground: "#FFFFFF", background: "#0891B2" },
            { text: "Automation", value: "automation", foreground: "#FFFFFF", background: "#DC2626" },
            { text: "Control", value: "control", foreground: "#FFFFFF", background: "#EA580C" },
            { text: "Command", value: "command", foreground: "#FFFFFF", background: "#7C2D12" },
            { text: "Request Info", value: "request_info", foreground: "#FFFFFF", background: "#374151" },
            { text: "Firmware Update", value: "firmware_update", foreground: "#FFFFFF", background: "#9333EA" },
          ],
        }),
        readonly: false,
        hidden: false,
        sort: 5,
        width: "half",
        translations: null,
        note: "Type of payload for this message",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // JSON payload editor with syntax highlighting
      {
        collection: "device_message_request",
        field: "payload_data",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 6,
        width: "full",
        translations: null,
        note: "JSON payload data to send to the device",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Compiled protobuf binary (auto-populated, hidden from users)
      {
        collection: "device_message_request",
        field: "payload_bytes",
        special: null,
        interface: null,
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 7,
        width: "full",
        translations: null,
        note: "Compiled protobuf binary data (auto-populated)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // SHA-256 hash for deduplication (system-generated, hidden)
      {
        collection: "device_message_request",
        field: "message_hash",
        special: null,
        interface: "input",
        options: JSON.stringify({
          readonly: true,
        }),
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: true,
        sort: 8,
        width: "full",
        translations: null,
        note: "SHA-256 hash for deduplication",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Message Relationship Fields
      // ====================================================================
      // Parent message for sequential operation chains
      {
        collection: "device_message_request",
        field: "parent_message_id",
        special: null,
        interface: "select-dropdown-m2o",
        options: null,
        display: "related-values",
        display_options: JSON.stringify({
          template: "{{device.identifier}} - {{payload_type}} ({{packet_id}})",
        }),
        readonly: false,
        hidden: false,
        sort: 9,
        width: "half",
        translations: null,
        note: "Parent message for sequential operations",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Correlation ID for bulk operations across devices
      {
        collection: "device_message_request",
        field: "correlation_id",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "UUID for grouping related messages",
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 10,
        width: "half",
        translations: null,
        note: "Correlation ID for grouping related messages across devices",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Delivery Control Fields
      // ====================================================================
      // Message lifecycle status with color-coded display
      {
        collection: "device_message_request",
        field: "status",
        special: null,
        interface: "select-dropdown",
        options: JSON.stringify({
          choices: [
            { text: "Pending", value: "pending" },
            { text: "Processing", value: "processing" },
            { text: "Sent", value: "sent" },
            { text: "Acknowledged", value: "acknowledged" },
            { text: "Failed", value: "failed" },
            { text: "Expired", value: "expired" },
            { text: "Cancelled", value: "cancelled" },
          ],
        }),
        display: "labels",
        display_options: JSON.stringify({
          choices: [
            { text: "Pending", value: "pending", foreground: "#FFFFFF", background: "#6B7280" },
            { text: "Processing", value: "processing", foreground: "#FFFFFF", background: "#2563EB" },
            { text: "Sent", value: "sent", foreground: "#FFFFFF", background: "#059669" },
            { text: "Acknowledged", value: "acknowledged", foreground: "#FFFFFF", background: "#10B981" },
            { text: "Failed", value: "failed", foreground: "#FFFFFF", background: "#DC2626" },
            { text: "Expired", value: "expired", foreground: "#FFFFFF", background: "#7C2D12" },
            { text: "Cancelled", value: "cancelled", foreground: "#FFFFFF", background: "#374151" },
          ],
        }),
        readonly: false,
        hidden: false,
        sort: 11,
        width: "half",
        translations: null,
        note: "Current status of the message",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Message priority level (1-10 scale)
      {
        collection: "device_message_request",
        field: "priority",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "5",
          min: 1,
          max: 10,
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 12,
        width: "half",
        translations: null,
        note: "Message priority (1=highest, 10=lowest)",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Scheduling & Timing Fields
      // ====================================================================
      // When to process this message
      {
        collection: "device_message_request",
        field: "scheduled_at",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 13,
        width: "half",
        translations: null,
        note: "When to process this message",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Message expiration timestamp (optional)
      {
        collection: "device_message_request",
        field: "expires_at",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: false,
        hidden: false,
        sort: 14,
        width: "half",
        translations: null,
        note: "When this message expires (optional)",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Successful transmission timestamp (read-only)
      {
        collection: "device_message_request",
        field: "sent_at",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 15,
        width: "half",
        translations: null,
        note: "When message was successfully sent",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Device acknowledgment timestamp (read-only)
      {
        collection: "device_message_request",
        field: "acknowledged_at",
        special: null,
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 16,
        width: "half",
        translations: null,
        note: "When device acknowledged receipt",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Retry & Error Handling Fields
      // ====================================================================
      // Current delivery attempt count (read-only)
      {
        collection: "device_message_request",
        field: "attempts",
        special: null,
        interface: "input",
        options: JSON.stringify({
          readonly: true,
        }),
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 17,
        width: "half",
        translations: null,
        note: "Number of delivery attempts made",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Maximum retry attempts before failure
      {
        collection: "device_message_request",
        field: "max_attempts",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "3",
          min: 1,
          max: 100,
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 18,
        width: "half",
        translations: null,
        note: "Maximum retry attempts before marking as failed",
        conditions: null,
        required: true,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Delay between retry attempts in seconds
      {
        collection: "device_message_request",
        field: "retry_delay_seconds",
        special: null,
        interface: "input",
        options: JSON.stringify({
          placeholder: "30",
          min: 0,
        }),
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 19,
        width: "half",
        translations: null,
        note: "Seconds to wait between retry attempts",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Last error message encountered (read-only)
      {
        collection: "device_message_request",
        field: "last_error",
        special: null,
        interface: "input-multiline",
        options: null,
        display: "raw",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 20,
        width: "full",
        translations: null,
        note: "Last error message encountered",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Standard Audit Fields
      // ====================================================================
      // Record creation timestamp (auto-populated)
      {
        collection: "device_message_request",
        field: "date_created",
        special: "date-created",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 21,
        width: "half",
        translations: null,
        note: "When this record was created",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // User who created this message request
      {
        collection: "device_message_request",
        field: "user_created",
        special: "user-created",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 22,
        width: "half",
        translations: null,
        note: "User who created this message request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Last update timestamp (auto-populated)
      {
        collection: "device_message_request",
        field: "date_updated",
        special: "date-updated",
        interface: "datetime",
        options: null,
        display: "datetime",
        display_options: JSON.stringify({ relative: true }),
        readonly: true,
        hidden: false,
        sort: 23,
        width: "half",
        translations: null,
        note: "When this record was last updated",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // User who last updated this request
      {
        collection: "device_message_request",
        field: "user_updated",
        special: "user-updated",
        interface: "select-dropdown-m2o",
        options: null,
        display: "user",
        display_options: null,
        readonly: true,
        hidden: false,
        sort: 24,
        width: "half",
        translations: null,
        note: "User who last updated this request",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // ====================================================================
      // Flexible Data Fields
      // ====================================================================
      // MQTT transport details and flexible metadata storage
      {
        collection: "device_message_request",
        field: "metadata",
        special: "json",
        interface: "input-code",
        options: JSON.stringify({
          language: "json",
          lineNumber: true,
        }),
        display: "formatted-json-value",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 25,
        width: "full",
        translations: null,
        note: "Transport details and flexible metadata storage",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
      // Human-readable notes and context
      {
        collection: "device_message_request",
        field: "notes",
        special: null,
        interface: "input-multiline",
        options: null,
        display: "raw",
        display_options: null,
        readonly: false,
        hidden: false,
        sort: 26,
        width: "full",
        translations: null,
        note: "Human-readable notes",
        conditions: null,
        required: false,
        group: null,
        validation: null,
        validation_message: null,
      },
    ]);

    // ========================================================================
    // Relationship Configuration
    // ========================================================================
    // Defines foreign key relationships for proper data integrity and
    // navigation within the Directus interface. Includes device associations,
    // self-referential parent chains, and audit user relationships.
    await tx.batchInsert("directus_relations", [
      // device_message_request -> device
      {
        many_collection: "device_message_request",
        many_field: "device",
        one_collection: "device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // device_message_request -> property_device
      {
        many_collection: "device_message_request",
        many_field: "property_device",
        one_collection: "property_device",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // device_message_request -> parent_message_id (self-referential)
      {
        many_collection: "device_message_request",
        many_field: "parent_message_id",
        one_collection: "device_message_request",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // device_message_request -> user_created
      {
        many_collection: "device_message_request",
        many_field: "user_created",
        one_collection: "directus_users",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
      // device_message_request -> user_updated
      {
        many_collection: "device_message_request",
        many_field: "user_updated",
        one_collection: "directus_users",
        one_field: null,
        one_collection_field: null,
        one_allowed_collections: null,
        junction_field: null,
        sort_field: null,
        one_deselect_action: "nullify",
      },
    ]);

    // ========================================================================
    // Security & Permission Configuration
    // ========================================================================
    // Implements comprehensive access control based on account ownership.
    // Users can only access message requests for devices in properties they
    // own or have admin access to. This ensures proper multi-tenant security.
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // READ permission - scoped by device property ownership through account
    await tx.batchInsert("directus_permissions", [
      {
        collection: "device_message_request",
        action: "read",
        permissions: {
          _and: [
            {
              device: {
                property_devices: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                property_devices: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "*",
        policy: policyId,
      },
    ]);

    // CREATE permission - allows creation of new message requests
    await tx.batchInsert("directus_permissions", [
      {
        collection: "device_message_request",
        action: "create",
        permissions: null,
        validation: null,
        presets: null,
        fields: "device,property_device,packet_id,payload_type,payload_data,parent_message_id,correlation_id,status,priority,scheduled_at,expires_at,max_attempts,retry_delay_seconds,metadata,notes",
        policy: policyId,
      },
    ]);

    // UPDATE permission - scoped by ownership, includes error field updates
    await tx.batchInsert("directus_permissions", [
      {
        collection: "device_message_request",
        action: "update",
        permissions: {
          _and: [
            {
              device: {
                property_devices: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                property_devices: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: "device,property_device,packet_id,payload_type,payload_data,parent_message_id,correlation_id,status,priority,scheduled_at,expires_at,max_attempts,retry_delay_seconds,last_error,metadata,notes",
        policy: policyId,
      },
    ]);

    // DELETE permission - scoped by ownership for cleanup operations
    await tx.batchInsert("directus_permissions", [
      {
        collection: "device_message_request",
        action: "delete",
        permissions: {
          _and: [
            {
              device: {
                property_devices: {
                  property: {
                    account: { users: { user: { _eq: "$CURRENT_USER" } } },
                  },
                },
              },
            },
            {
              device: {
                property_devices: {
                  property: { account: { users: { role: { _eq: "admin" } } } },
                },
              },
            },
          ],
        },
        validation: null,
        presets: null,
        fields: null,
        policy: policyId,
      },
    ]);
  });
}

/**
 * Migration rollback - removes all Directus configuration
 * 
 * Cleanly removes all Directus metadata for device_message_request collection
 * in reverse order to maintain referential integrity. This includes permissions,
 * relationships, fields, and the collection definition.
 * 
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    const policyId = "b6e0a767-dc9a-478d-b588-61aa700a519a";

    // Remove configuration in reverse order to maintain referential integrity
    
    // 1) Remove permissions first (depend on collection)
    await tx.raw(`
      COMMENT ON TABLE public.device_message_request IS NULL;
    `);
    await tx("directus_permissions")
      .where({
        collection: "device_message_request",
        policy: policyId,
      })
      .del();

    // 2) Remove relations (depend on fields)
    await tx("directus_relations")
      .where({ many_collection: "device_message_request" })
      .del();

    // 3) Remove fields (depend on collection)
    await tx("directus_fields")
      .where({ collection: "device_message_request" })
      .del();

    // 4) Remove collection last
    await tx("directus_collections")
      .where({ collection: "device_message_request" })
      .del();
  });
}