/**
 * Migration to create current_lic_packet table for storing most recent LIC packets
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create current_lic_packet table
    await tx.schema.createTable('current_lic_packet', (table) => {
      table.increments('id').primary();
      table.uuid('device').notNullable();
      table.timestamp('date_created').notNullable().defaultTo(knex.fn.now());
      table.timestamp('packet_date').notNullable().defaultTo(knex.fn.now());
      table.string('payload_type', 50).notNullable();
      table.jsonb('payload_data').notNullable();
      
      // Foreign key constraint
      table.foreign('device').references('id').inTable('device');
      
      // Unique constraint for (device, payload_type) to ensure only most recent packet per combination
      table.unique(['device', 'payload_type'], 'current_lic_packet_device_payload_type_unique');
      
      // Performance index for (device, packet_date)
      table.index(['device', 'packet_date'], 'current_lic_packet_device_packet_date_idx');
    });
  });
}

/**
 * Migration to drop current_lic_packet table
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    await tx.schema.dropTableIfExists('current_lic_packet');
  });
}