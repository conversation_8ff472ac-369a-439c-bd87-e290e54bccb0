/**
 * Migration to create lic_packet table as TimescaleDB hypertable with monthly chunking and compression
 * @param {import('knex').Knex} knex
 */
export async function up(knex) {
  await knex.transaction(async (tx) => {
    // Create lic_packet table with same structure as current_lic_packet
    await tx.schema.createTable("lic_packet", (table) => {
      table.bigIncrements("id");
      table.uuid("device").notNullable();
      table.timestamp("date_created").notNullable().defaultTo(knex.fn.now());
      table.timestamp("packet_date").notNullable().defaultTo(knex.fn.now());
      // Timescaledb best practices recommend using text for variable length strings
      table.text("payload_type").notNullable();
      table.jsonb("payload_data").notNullable();

      // Foreign key constraint
      table.foreign("device").references("id").inTable("device");

      // Index for time-series queries (will be created before hypertable conversion)
      table.index(["packet_date"], "lic_packet_packet_date_idx");
      table.index(
        ["device", "packet_date"],
        "lic_packet_device_packet_date_idx"
      );
      // Primary key constraint for (device, packet_date, payload_type)
      table.primary(["device", "packet_date", "payload_type"]);
      // table.index(
      //   ["payload_type", "packet_date"],
      //   "lic_packet_payload_type_packet_date_idx"
      // );
    });

    // Ensure TimescaleDB extension is enabled
    await tx.raw(`CREATE EXTENSION IF NOT EXISTS timescaledb;`);

    // Convert table to TimescaleDB hypertable with monthly chunking
    await tx.raw(`
      SELECT create_hypertable(
        'lic_packet',
        'packet_date',
        chunk_time_interval => INTERVAL '1 month',
        if_not_exists => TRUE
      );
    `);

    // Add unique constraint after hypertable creation (must include partitioning column)
    // await tx.raw(`
    //   ALTER TABLE lic_packet
    //   ADD CONSTRAINT lic_packet_device_packet_date_payload_type_unique
    //   UNIQUE (device, packet_date, payload_type);
    // `);

    // Add compression policy for data older than 3 months
    await tx.raw(`
      ALTER TABLE lic_packet SET (
        timescaledb.compress,
        timescaledb.compress_segmentby = 'device, payload_type'
      );
    `);

    await tx.raw(`
      SELECT add_compression_policy(
        'lic_packet',
        INTERVAL '3 months'
      );
    `);

    // Add retention policy to automatically drop data older than 2 years
    // await tx.raw(`
    //   SELECT add_retention_policy(
    //     'lic_packet',
    //     INTERVAL '2 years'
    //   );
    // `);
  });
}

/**
 * Migration to drop lic_packet hypertable
 * @param {import('knex').Knex} knex
 */
export async function down(knex) {
  await knex.transaction(async (tx) => {
    // Remove compression and retention policies
    await tx.raw(`
      SELECT remove_compression_policy('lic_packet', if_exists => true);
    `);

    await tx.raw(`
      SELECT remove_retention_policy('lic_packet', if_exists => true);
    `);

    // Remove unique constraint before dropping table
    // await tx.raw(`
    //   ALTER TABLE lic_packet
    //   DROP CONSTRAINT IF EXISTS lic_packet_device_packet_date_payload_type_unique;
    // `);

    // Drop hypertable (this automatically removes chunks)
    await tx.schema.dropTableIfExists("lic_packet");
  });
}
