import type {
  DirectusRelationFieldType,
} from "@/utils/types";
import type { Model } from "./common";
import type { Device } from "./device";
import type { PropertyDevice } from "./property-device";

export const DEVICE_MESSAGE_REQUEST_STATUS_VALUES = [
  "pending",
  "processing", 
  "sent",
  "acknowledged",
  "failed",
  "expired",
  "cancelled",
] as const;

export type DeviceMessageRequestStatus = (typeof DEVICE_MESSAGE_REQUEST_STATUS_VALUES)[number];

export const DEVICE_MESSAGE_REQUEST_PAYLOAD_TYPE_VALUES = [
  "config",
  "devices",
  "scheduling",
  "dev_scheduling",
  "automation",
  "control",
  "command",
  "request_info",
  "firmware_update",
] as const;

export type DeviceMessageRequestPayloadType = (typeof DEVICE_MESSAGE_REQUEST_PAYLOAD_TYPE_VALUES)[number];

export type DeviceMessageRequestRelationsTypes = {
  device: DirectusRelationFieldType<Device>;
  property_device: DirectusRelationFieldType<PropertyDevice>;
  parent_message_id: DirectusRelationFieldType<DeviceMessageRequest>;
};

export type DeviceMessageRequestDefaultRelationsTypes = {
  device: DirectusRelationFieldType<Device>;
  property_device: DirectusRelationFieldType<PropertyDevice>;
  parent_message_id: DirectusRelationFieldType<DeviceMessageRequest>;
};

export interface DeviceMessageRequest<
  Types extends Partial<DeviceMessageRequestRelationsTypes> = DeviceMessageRequestDefaultRelationsTypes
> extends Model {
  id: string;
  
  // Device Relationships
  device: Types["device"];
  property_device: Types["property_device"];

  // Message Content
  packet_id: number;
  payload_type: DeviceMessageRequestPayloadType;
  payload_data: Record<string, any>;
  payload_bytes: ArrayBuffer | null;
  message_hash: string | null;

  // Message Relationships & Correlation
  parent_message_id: Types["parent_message_id"];
  correlation_id: string | null;

  // Delivery Control
  status: DeviceMessageRequestStatus;
  priority: number;

  // Scheduling & Timing
  scheduled_at: string;
  expires_at: string | null;
  sent_at: string | null;
  acknowledged_at: string | null;

  // Retry & Error Handling
  attempts: number;
  max_attempts: number;
  retry_delay_seconds: number | null;
  last_error: string | null;

  // Standard Audit Fields
  date_created: string;
  user_created: string | null;
  date_updated: string;
  user_updated: string | null;
  metadata: Record<string, any> | null;
  notes: string | null;
}

export type DeviceMessageRequestMetadata = {
  mqtt_topic?: string;
  mqtt_qos?: number;
  mqtt_retain?: boolean;
  transport_protocol?: string;
  delivery_attempt_timestamps?: string[];
  custom_routing?: {
    mesh_gateway?: string;
  };
};